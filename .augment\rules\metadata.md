---
type: "manual"
---

1. <PERSON><PERSON><PERSON> bah<PERSON>, tanpa komentar. <PERSON><PERSON>lasan diskusi hanya dalam Bahasa Indonesia. <PERSON>i adalah aplikasi modular menggunakan gaya "views, models dan services".
2. <PERSON><PERSON> ubah fitur yang ada. <PERSON><PERSON> per<PERSON>n atau tambahan yang perlu.
3. <PERSON><PERSON><PERSON> struktur sebelum ubah. Hindari konflik dan duplikasi.
4. Fungsi baru harus modular. Satu file, terintegrasi, kompatibel.
5. Jika file terlalu besar buatkan di-file baru tetapi terintegrasi dengan akurat.
6. Gunakan javascript biasa bukan ES6 Modules.
7. <PERSON><PERSON> pernah buat file test.